import React, { useState } from 'react';
import {
  FormControl,
  Select,
  MenuItem,
  IconButton,
  Box,
  Typography,
  Tooltip,
  Snackbar,
  Alert,
} from '@mui/material';
import { Icon } from '../features/create-requests/assets';
type RequestTemplate = Api.GetRequestTemplatesResponse[number];

import { Api } from '../shared';
import { generateResultTitle, generateResultContent } from '../lib/template-generator';

interface RequestTemplateSelectProps {
  templates: RequestTemplate[];
  selectedTemplate: RequestTemplate | null;
  onTemplateSelect: (template: RequestTemplate | null) => void;
  onTemplateEdit: (template: RequestTemplate) => void;
  onTemplateCreate: () => void;
  onTemplateDelete: (template: RequestTemplate) => void;
  context: unknown; // Context for template generation
}

export const RequestTemplateSelect: React.FC<RequestTemplateSelectProps> = ({
  templates,
  selectedTemplate,
  onTemplateSelect,
  onTemplateEdit,
  onTemplateCreate,
  onTemplateDelete,
  context,
}) => {
  const [copySuccess, setCopySuccess] = useState(false);

  const handleTemplateChange = (templateId: string) => {
    if (templateId === '') {
      onTemplateSelect(null);
    } else {
      const template = templates.find(t => t.id === templateId);
      if (template) {
        onTemplateSelect(template);
      }
    }
  };

  const handleCopy = async (template: RequestTemplate) => {
    try {
      const generatedTitle = generateResultTitle(template.title, context);
      const generatedContent = generateResultContent(template.content, context);
      const textToCopy = `${generatedTitle}\n\n${generatedContent}`;

      await navigator.clipboard.writeText(textToCopy);
      setCopySuccess(true);
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
    }
  };

  const handleDelete = async (template: RequestTemplate) => {
    if (window.confirm(`Are you sure you want to delete template "${template.name}"?`)) {
      onTemplateDelete(template);
    }
  };

  return (
    <>
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
        <FormControl size="small" sx={{ flexGrow: 1, minWidth: 200 }}>
          <Select
            value={selectedTemplate?.id || ''}
            onChange={(e) => handleTemplateChange(e.target.value)}
            displayEmpty
            disabled={loading}
            sx={{
              fontSize: '12px',
              height: '32px',
              '& .MuiOutlinedInput-notchedOutline': {
                borderRadius: 0,
              },
              '& .Mui-focused .MuiOutlinedInput-notchedOutline': {
                border: '2px solid green !important',
              },
            }}
          >
            <MenuItem value="">
              <Typography sx={{ fontSize: '12px', color: '#999' }}>
                Select template
              </Typography>
            </MenuItem>
            {templates.map((template) => (
              <MenuItem key={template.id} value={template.id}>
                <Typography sx={{ fontSize: '12px' }}>
                  {template.name}
                </Typography>
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        {/* Action buttons for selected template */}
        {selectedTemplate && (
          <Box sx={{ display: 'flex', gap: 0.5 }}>
            <Tooltip title="Copy generated result to clipboard">
              <IconButton
                size="small"
                onClick={() => handleCopy(selectedTemplate)}
                sx={{
                  padding: '4px',
                  width: '32px',
                  height: '32px',
                }}
              >
                <Icon.Copy />
              </IconButton>
            </Tooltip>
            <Tooltip title="Edit template">
              <IconButton
                size="small"
                onClick={() => onTemplateEdit(selectedTemplate)}
                sx={{
                  padding: '4px',
                  width: '32px',
                  height: '32px',
                }}
              >
                <Icon.Edit />
              </IconButton>
            </Tooltip>
            <Tooltip title="Delete template">
              <IconButton
                size="small"
                onClick={() => handleDelete(selectedTemplate)}
                sx={{
                  padding: '4px',
                  width: '32px',
                  height: '32px',
                }}
              >
                <Icon.Trash />
              </IconButton>
            </Tooltip>
          </Box>
        )}

        <Tooltip title="Add new template">
          <IconButton
            onClick={onTemplateCreate}
            sx={{
              backgroundColor: '#70B57D',
              color: 'white',
              width: '32px',
              height: '32px',
              '&:hover': {
                backgroundColor: '#5a9a66',
              },
            }}
          >
            <Icon.Plus />
          </IconButton>
        </Tooltip>
      </Box>

      <Snackbar
        open={copySuccess}
        autoHideDuration={2000}
        onClose={() => setCopySuccess(false)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={() => setCopySuccess(false)} severity="success" sx={{ width: '100%' }}>
          Template copied to clipboard!
        </Alert>
      </Snackbar>
    </>
  );
};
