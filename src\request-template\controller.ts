import type { Request, Response } from "express";
import type { RequestTemplateService } from ".";

import { Router } from "express";
import { Api } from "../shared";
import { Injectable } from "../utils";
import { bodyValidator, paramsValidator, requireAuth } from "../middleware";

export class RequestTemplateController extends Injectable {
    private readonly requestTemplateService!: RequestTemplateService;

    async getForUser(req: Request, res: Response) {
        const requestTemplates = await this.requestTemplateService.getForUser({
            userId: req.session.userId!,
        });

        res.json(requestTemplates);
    }

    async create(req: Request, res: Response) {
        const data = req.body as Api.CreateRequestTemplateRequest;

        const requestTemplate = await this.requestTemplateService.create({
            userId: req.session.userId!,
            ...data,
        });

        res.status(201).json(requestTemplate);
    }

    async update(req: Request, res: Response) {
        const data = {
            ...req.params as Api.UpdateRequestTemplateRequestParams,
            ...req.body as Api.UpdateRequestTemplateRequestBody,
        };

        const requestTemplate = await this.requestTemplateService.update({
            userId: req.session.userId!,
            ...data,
        });

        res.json(requestTemplate);
    }

    async delete(req: Request, res: Response) {
        const data = {
            ...req.params as Api.DeleteRequestTemplateRequestParams,
        };

        await this.requestTemplateService.delete({
            userId: req.session.userId!,
            ...data,
        });

        res.sendStatus(204);
    }

    getRouter() {
        const router = Router();

        router.use(requireAuth);

        router.get(
            "/",
            this.getForUser.bind(this),
        );

        router.post(
            "/",
            bodyValidator(Api.CreateRequestTemplateRequest),
            this.create.bind(this),
        );

        router.put(
            "/:id",
            paramsValidator(Api.UpdateRequestTemplateRequestParams),
            bodyValidator(Api.UpdateRequestTemplateRequestBody),
            this.update.bind(this),
        );

        router.delete(
            "/:id",
            paramsValidator(Api.DeleteRequestTemplateRequestParams),
            this.delete.bind(this),
        );

        return router;
    }
}
