// Template generation functions with real implementation
// Based on the backend template system from backend/src/test/templates.ts

interface TemplateContext {
  ordinal: number | null;
  service: string | null;
  incoterms: string | null;
  from: {
    country: string | null;
    city: string | null;
    address: string | null;
    zipcode: string | null;
  };
  to: {
    country: string | null;
    city: string | null;
    address: string | null;
    zipcode: string | null;
  };
  goods: {
    description: string | null;
    hsCodes: string | null;
    cost: number;
    currency: string | null;
  };
  services: {
    additional: string[];
    dangerous: string[];
  };
  packages: Array<{
    quantity: number;
    length: number | null;
    width: number | null;
    height: number | null;
    volume: number | null;
    weight: number | null;
    type: string | null;
  }>;
  total: {
    quantity: number;
    weight: number;
    volume: number;
  };
}

interface PackageData {
  quantity: number;
  length: number;
  width: number;
  height: number;
  volume: number;
  weight: number;
  type: string;
}

interface TemplateData {
  packages?: PackageData[];
  [key: string]: string | number | PackageData[] | undefined;
}

function fillTemplate(template: string, data: TemplateData): string {
  const packagesTagIndex = template.indexOf("{packages}");
  const isHasPackagesTag = packagesTagIndex !== -1;

  if (isHasPackagesTag && template.indexOf("{packages}", packagesTagIndex + 1) !== -1) {
    throw new Error("Template cannot have multiple {packages} tags");
  }

  return template
    .replace(
      /\{(.+?)\}/g,
      (_, name) => {
        if (name === "packages" || name === "/packages" || name.startsWith("package.")) {
          return `{${name}}`;
        }

        if (name in data) {
          const value = data[name];
          return value !== null && value !== undefined ? String(value) : "";
        }

        return `{${name}}`;
      },
    )
    .replace(
      /\{packages\}((?:.|\r?\n)+?)\{\/packages\}/gm,
      (_, match: string) => {
        return data.packages?.map(
          (_package: PackageData) => {
            return match.replace(
              /\{package\.(.+?)\}/g,
              (_, name) => {
                if (name in _package) {
                  const value = _package[name as keyof PackageData];
                  return value !== null && value !== undefined ? String(value) : "";
                }

                return `{package.${name}}`;
              },
            );
          },
        ).join("\n") || "";
      },
    );
}

export function generateResultTitle(template: string, context: unknown): string {
  const ctx = context as TemplateContext;

  // Prepare data for template filling
  const templateData = {
    "ordinal": ctx.ordinal ? `IN-${String(ctx.ordinal).padStart(3, "0")}` : "",
    "incoterms": ctx.incoterms || "",
    "from.city": ctx.from.city || "",
    "from.country": ctx.from.country || "",
    "to.city": ctx.to.city || "",
    "to.country": ctx.to.country || "",
    "total.weight": ctx.total.weight > 0 ? `${ctx.total.weight.toFixed(2)} kg` : "",
    "total.volume": ctx.total.volume > 0 ? `${ctx.total.volume.toFixed(2)} cbm` : "",
  };

  return fillTemplate(template, templateData);
}

export function generateResultContent(template: string, context: unknown): string {
  const ctx = context as TemplateContext;

  // Prepare pickup address
  const pickupAddressParts = [
    ctx.from.address,
    ctx.from.city,
    ctx.from.zipcode,
    ctx.from.country
  ].filter(Boolean);
  const pickupAddress = pickupAddressParts.join(", ");

  // Prepare delivery address
  const deliveryAddressParts = [
    ctx.to.address,
    ctx.to.city,
    ctx.to.zipcode,
    ctx.to.country
  ].filter(Boolean);
  const deliveryAddress = deliveryAddressParts.join(", ");

  // Prepare cost of goods
  const costOfGoods = ctx.goods.cost && ctx.goods.currency
    ? `${ctx.goods.currency} ${ctx.goods.cost}`
    : "";

  // Prepare data for template filling
  const templateData = {
    "service": ctx.service || "",
    "incoterms": ctx.incoterms || "",
    "from.country": ctx.from.country || "",
    "from.city": ctx.from.city || "",
    "from.address": pickupAddress,
    "to.country": ctx.to.country || "",
    "to.city": ctx.to.city || "",
    "to.address": deliveryAddress,
    "goods.description": ctx.goods.description || "",
    "goods.hsCodes": ctx.goods.hsCodes || "",
    "goods.cost": costOfGoods,
    "services.additional": ctx.services.additional.join(", "),
    "services.dangerous": ctx.services.dangerous.join(", "),
    "total.quantity": ctx.total.quantity > 0 ? `${ctx.total.quantity} pcs` : "",
    "total.weight": ctx.total.weight > 0 ? `${ctx.total.weight.toFixed(2)} kg` : "",
    "total.volume": ctx.total.volume > 0 ? `${ctx.total.volume.toFixed(2)} cbm` : "",
    packages: ctx.packages.map(pkg => ({
      quantity: pkg.quantity || 0,
      length: pkg.length || 0,
      width: pkg.width || 0,
      height: pkg.height || 0,
      volume: pkg.volume || 0,
      weight: pkg.weight || 0,
      type: pkg.type || "",
    })),
  };

  return fillTemplate(template, templateData);
}

export type TemplateTag = {
  tag: string;
  description: string;
};

// Template tags for title and content fields
export const TITLE_TEMPLATE_TAGS = [
  {
    tag: '{ordinal}',
    description: 'Request ordinal number (e.g., IN-001)',
  },
  {
    tag: '{incoterms}',
    description: 'Incoterms (e.g., EXW, FOB, CIF)',
  },
  {
    tag: '{from.city}',
    description: 'Origin city name',
  },
  {
    tag: '{from.country}',
    description: 'Origin country name',
  },
  {
    tag: '{to.city}',
    description: 'Destination city name',
  },
  {
    tag: '{to.country}',
    description: 'Destination country name',
  },
  {
    tag: '{total.weight}',
    description: 'Total weight with unit (e.g., 150.50 kg)',
  },
  {
    tag: '{total.volume}',
    description: 'Total volume with unit (e.g., 2.45 cbm)',
  },
];

export const CONTENT_TEMPLATE_TAGS = [
  {
    tag: '{service}',
    description: 'Requested service type (e.g., Air freight, Sea freight)',
  },
  {
    tag: '{incoterms}',
    description: 'Incoterms (e.g., EXW, FOB, CIF)',
  },
  {
    tag: '{from.country}',
    description: 'Origin country name',
  },
  {
    tag: '{from.city}',
    description: 'Origin city name',
  },
  {
    tag: '{from.address}',
    description: 'Complete pickup address including city, zipcode, and country',
  },
  {
    tag: '{to.country}',
    description: 'Destination country name',
  },
  {
    tag: '{to.city}',
    description: 'Destination city name',
  },
  {
    tag: '{to.address}',
    description: 'Complete delivery address including city, zipcode, and country',
  },
  {
    tag: '{goods.description}',
    description: 'Description of goods being shipped',
  },
  {
    tag: '{goods.hsCodes}',
    description: 'Harmonized System codes for customs',
  },
  {
    tag: '{goods.cost}',
    description: 'Cost of goods with currency (e.g., USD 1500)',
  },
  {
    tag: '{services.additional}',
    description: 'Comma-separated list of additional services',
  },
  {
    tag: '{services.dangerous}',
    description: 'Comma-separated list of dangerous goods',
  },
  {
    tag: '{total.quantity}',
    description: 'Total quantity with unit (e.g., 25 pcs)',
  },
  {
    tag: '{total.weight}',
    description: 'Total weight with unit (e.g., 150.50 kg)',
  },
  {
    tag: '{total.volume}',
    description: 'Total volume with unit (e.g., 2.45 cbm)',
  },
  {
    tag: '{packages}',
    description: 'Start of package loop - use with {/packages}',
  },
  {
    tag: '{/packages}',
    description: 'End of package loop',
  },
  {
    tag: '{package.quantity}',
    description: 'Package quantity (use inside {packages} loop)',
  },
  {
    tag: '{package.length}',
    description: 'Package length in cm (use inside {packages} loop)',
  },
  {
    tag: '{package.width}',
    description: 'Package width in cm (use inside {packages} loop)',
  },
  {
    tag: '{package.height}',
    description: 'Package height in cm (use inside {packages} loop)',
  },
  {
    tag: '{package.volume}',
    description: 'Package volume in cbm (use inside {packages} loop)',
  },
  {
    tag: '{package.weight}',
    description: 'Package weight in kg (use inside {packages} loop)',
  },
  {
    tag: '{package.type}',
    description: 'Package type (use inside {packages} loop)',
  },
];
