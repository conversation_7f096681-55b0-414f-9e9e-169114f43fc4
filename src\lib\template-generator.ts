// Mock template generation functions
// These will be replaced with actual template processing logic later

export function generateResultTitle(template: string, context: unknown): string {
  // Mock implementation - returns template unchanged
  return template;
}

export function generateResultContent(template: string, context: unknown): string {
  // Mock implementation - returns template unchanged
  return template;
}

export type TemplateTag = {
  tag: string;
  description: string;
};

// Template tags for title and content fields
export const TITLE_TEMPLATE_TAGS = [
  {
    tag: '{{ ORDINAL }}',
    description: 'Request ordinal number (e.g., IN-001)',
  },
  {
    tag: '{{ INCOTERMS }}',
    description: 'Incoterms (e.g., EXW, FOB, CIF)',
  },
  {
    tag: '{{ ORIGIN_CITY }}',
    description: 'Origin city name',
  },
  {
    tag: '{{ ORIGIN_COUNTRY }}',
    description: 'Origin country name',
  },
  {
    tag: '{{ DESTINATION_CITY }}',
    description: 'Destination city name',
  },
  {
    tag: '{{ DESTINATION_COUNTRY }}',
    description: 'Destination country name',
  },
  {
    tag: '{{ TOTAL_WEIGHT }}',
    description: 'Total weight in kg',
  },
  {
    tag: '{{ TOTAL_VOLUME }}',
    description: 'Total volume in cbm',
  },
];

export const CONTENT_TEMPLATE_TAGS = [
  {
    tag: '{{ SERVICE }}',
    description: 'Requested service type',
  },
  {
    tag: '{{ INCOTERMS }}',
    description: 'Incoterms (e.g., EXW, FOB, CIF)',
  },
  {
    tag: '{{ ORIGIN_COUNTRY }}',
    description: 'Origin country name',
  },
  {
    tag: '{{ DESTINATION_COUNTRY }}',
    description: 'Destination country name',
  },
  {
    tag: '{{ PICKUP_ADDRESS }}',
    description: 'Full pickup address',
  },
  {
    tag: '{{ DELIVERY_ADDRESS }}',
    description: 'Full delivery address',
  },
  {
    tag: '{{ DESCRIPTION_OF_GOODS }}',
    description: 'Description of goods',
  },
  {
    tag: '{{ HS_CODES }}',
    description: 'HS codes',
  },
  {
    tag: '{{ COST_OF_GOODS }}',
    description: 'Cost of goods with currency',
  },
  {
    tag: '{{ ADDITIONAL_SERVICES }}',
    description: 'Additional services list',
  },
  {
    tag: '{{ DANGEROUS_GOODS }}',
    description: 'Dangerous goods list',
  },
  {
    tag: '{{ PACKAGE_DETAILS }}',
    description: 'Detailed package information',
  },
  {
    tag: '{{ TOTAL_QUANTITY }}',
    description: 'Total quantity in pieces',
  },
  {
    tag: '{{ TOTAL_WEIGHT }}',
    description: 'Total weight in kg',
  },
  {
    tag: '{{ TOTAL_VOLUME }}',
    description: 'Total volume in cbm',
  },
];
