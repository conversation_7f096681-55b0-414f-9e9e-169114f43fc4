// Template generation functions with real implementation
// Based on the backend template system from backend/src/test/templates.ts

interface TemplateContext {
  ordinal: number | null;
  service: string | null;
  incoterms: string | null;
  origin: string | null;
  destination: string | null;
  pickupCity: string | null;
  pickupAddress: string | null;
  pickupZipcode: string | null;
  isPickupRequired: boolean;
  deliveryCity: string | null;
  deliveryAddress: string | null;
  deliveryZipcode: string | null;
  isDeliveryRequired: boolean;
  descriptionOfGoods: string | null;
  hsCodes: string | null;
  costOfGoods: number;
  currency: string | null;
  additionalServices: string[];
  dangerousGoods: string[];
  packages: Array<{
    quantity: number;
    length: number | null;
    width: number | null;
    height: number | null;
    volume: number | null;
    weight: number | null;
    type: string | null;
  }>;
  totalQuantity: number;
  totalWeight: number;
  totalVolume: number;
}

interface PackageData {
  quantity: number;
  length: number;
  width: number;
  height: number;
  volume: number;
  weight: number;
  type: string;
}

interface TemplateData {
  packages?: PackageData[];
  [key: string]: string | number | PackageData[] | undefined;
}

function fillTemplate(template: string, data: TemplateData): string {
  const packagesTagIndex = template.indexOf("{packages}");
  const isHasPackagesTag = packagesTagIndex !== -1;

  if (isHasPackagesTag && template.indexOf("{packages}", packagesTagIndex + 1) !== -1) {
    throw new Error("Template cannot have multiple {packages} tags");
  }

  return template
    .replace(
      /\{(.+?)\}/g,
      (_, name) => {
        if (name === "packages" || name === "/packages" || name.startsWith("package.")) {
          return `{${name}}`;
        }

        if (name in data) {
          const value = data[name];
          return value !== null && value !== undefined ? String(value) : "";
        }

        return `{${name}}`;
      },
    )
    .replace(
      /\{packages\}((?:.|\r?\n)+?)\{\/packages\}/gm,
      (_, match: string) => {
        return data.packages?.map(
          (_package: PackageData) => {
            return match.replace(
              /\{package\.(.+?)\}/g,
              (_, name) => {
                if (name in _package) {
                  const value = _package[name as keyof PackageData];
                  return value !== null && value !== undefined ? String(value) : "";
                }

                return `{package.${name}}`;
              },
            );
          },
        ).join("\n") || "";
      },
    );
}

export function generateResultTitle(template: string, context: unknown): string {
  const ctx = context as TemplateContext;

  // Prepare data for template filling
  const templateData = {
    "ORDINAL": ctx.ordinal ? `IN-${String(ctx.ordinal).padStart(3, "0")}` : "",
    "INCOTERMS": ctx.incoterms || "",
    "ORIGIN_CITY": ctx.pickupCity || "",
    "ORIGIN_COUNTRY": ctx.origin || "",
    "DESTINATION_CITY": ctx.deliveryCity || "",
    "DESTINATION_COUNTRY": ctx.destination || "",
    "TOTAL_WEIGHT": ctx.totalWeight > 0 ? `${ctx.totalWeight.toFixed(2)} kg` : "",
    "TOTAL_VOLUME": ctx.totalVolume > 0 ? `${ctx.totalVolume.toFixed(2)} cbm` : "",
  };

  return fillTemplate(template, templateData);
}

export function generateResultContent(template: string, context: unknown): string {
  const ctx = context as TemplateContext;

  // Prepare pickup address
  const pickupAddressParts = [
    ctx.pickupAddress,
    ctx.pickupCity,
    ctx.pickupZipcode,
    ctx.origin
  ].filter(Boolean);
  const pickupAddress = pickupAddressParts.join(", ");

  // Prepare delivery address
  const deliveryAddressParts = [
    ctx.deliveryAddress,
    ctx.deliveryCity,
    ctx.deliveryZipcode,
    ctx.destination
  ].filter(Boolean);
  const deliveryAddress = deliveryAddressParts.join(", ");

  // Prepare cost of goods
  const costOfGoods = ctx.costOfGoods && ctx.currency
    ? `${ctx.currency} ${ctx.costOfGoods}`
    : "";

  // Prepare data for template filling
  const templateData = {
    "SERVICE": ctx.service || "",
    "INCOTERMS": ctx.incoterms || "",
    "ORIGIN_COUNTRY": ctx.origin || "",
    "DESTINATION_COUNTRY": ctx.destination || "",
    "PICKUP_ADDRESS": pickupAddress,
    "DELIVERY_ADDRESS": deliveryAddress,
    "DESCRIPTION_OF_GOODS": ctx.descriptionOfGoods || "",
    "HS_CODES": ctx.hsCodes || "",
    "COST_OF_GOODS": costOfGoods,
    "ADDITIONAL_SERVICES": ctx.additionalServices.join(", "),
    "DANGEROUS_GOODS": ctx.dangerousGoods.join(", "),
    "TOTAL_QUANTITY": ctx.totalQuantity > 0 ? `${ctx.totalQuantity} pcs` : "",
    "TOTAL_WEIGHT": ctx.totalWeight > 0 ? `${ctx.totalWeight.toFixed(2)} kg` : "",
    "TOTAL_VOLUME": ctx.totalVolume > 0 ? `${ctx.totalVolume.toFixed(2)} cbm` : "",
    packages: ctx.packages.map(pkg => ({
      quantity: pkg.quantity || 0,
      length: pkg.length || 0,
      width: pkg.width || 0,
      height: pkg.height || 0,
      volume: pkg.volume || 0,
      weight: pkg.weight || 0,
      type: pkg.type || "",
    })),
  };

  return fillTemplate(template, templateData);
}

export type TemplateTag = {
  tag: string;
  description: string;
};

// Template tags for title and content fields
export const TITLE_TEMPLATE_TAGS = [
  {
    tag: '{ORDINAL}',
    description: 'Request ordinal number (e.g., IN-001)',
  },
  {
    tag: '{INCOTERMS}',
    description: 'Incoterms (e.g., EXW, FOB, CIF)',
  },
  {
    tag: '{ORIGIN_CITY}',
    description: 'Origin city name',
  },
  {
    tag: '{ORIGIN_COUNTRY}',
    description: 'Origin country name',
  },
  {
    tag: '{DESTINATION_CITY}',
    description: 'Destination city name',
  },
  {
    tag: '{DESTINATION_COUNTRY}',
    description: 'Destination country name',
  },
  {
    tag: '{TOTAL_WEIGHT}',
    description: 'Total weight with unit (e.g., 150.50 kg)',
  },
  {
    tag: '{TOTAL_VOLUME}',
    description: 'Total volume with unit (e.g., 2.45 cbm)',
  },
];

export const CONTENT_TEMPLATE_TAGS = [
  {
    tag: '{SERVICE}',
    description: 'Requested service type (e.g., Air freight, Sea freight)',
  },
  {
    tag: '{INCOTERMS}',
    description: 'Incoterms (e.g., EXW, FOB, CIF)',
  },
  {
    tag: '{ORIGIN_COUNTRY}',
    description: 'Origin country name',
  },
  {
    tag: '{DESTINATION_COUNTRY}',
    description: 'Destination country name',
  },
  {
    tag: '{PICKUP_ADDRESS}',
    description: 'Complete pickup address including city, zipcode, and country',
  },
  {
    tag: '{DELIVERY_ADDRESS}',
    description: 'Complete delivery address including city, zipcode, and country',
  },
  {
    tag: '{DESCRIPTION_OF_GOODS}',
    description: 'Description of goods being shipped',
  },
  {
    tag: '{HS_CODES}',
    description: 'Harmonized System codes for customs',
  },
  {
    tag: '{COST_OF_GOODS}',
    description: 'Cost of goods with currency (e.g., USD 1500)',
  },
  {
    tag: '{ADDITIONAL_SERVICES}',
    description: 'Comma-separated list of additional services',
  },
  {
    tag: '{DANGEROUS_GOODS}',
    description: 'Comma-separated list of dangerous goods',
  },
  {
    tag: '{TOTAL_QUANTITY}',
    description: 'Total quantity with unit (e.g., 25 pcs)',
  },
  {
    tag: '{TOTAL_WEIGHT}',
    description: 'Total weight with unit (e.g., 150.50 kg)',
  },
  {
    tag: '{TOTAL_VOLUME}',
    description: 'Total volume with unit (e.g., 2.45 cbm)',
  },
  {
    tag: '{packages}',
    description: 'Start of package loop - use with {/packages}',
  },
  {
    tag: '{/packages}',
    description: 'End of package loop',
  },
  {
    tag: '{package.quantity}',
    description: 'Package quantity (use inside {packages} loop)',
  },
  {
    tag: '{package.length}',
    description: 'Package length in cm (use inside {packages} loop)',
  },
  {
    tag: '{package.width}',
    description: 'Package width in cm (use inside {packages} loop)',
  },
  {
    tag: '{package.height}',
    description: 'Package height in cm (use inside {packages} loop)',
  },
  {
    tag: '{package.volume}',
    description: 'Package volume in cbm (use inside {packages} loop)',
  },
  {
    tag: '{package.weight}',
    description: 'Package weight in kg (use inside {packages} loop)',
  },
  {
    tag: '{package.type}',
    description: 'Package type (use inside {packages} loop)',
  },
];
